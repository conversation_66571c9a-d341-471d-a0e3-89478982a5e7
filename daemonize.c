#define _POSIX_C_SOURCE 200809L
#include "daemonize.h"
#include "log.h"
#include <errno.h>
#include <signal.h>

// Global variable for cleanup in signal handlers
static volatile sig_atomic_t daemon_should_exit = 0;

void write_pid_file() {
  FILE *f = fopen(PID_FILE, "w");
  if (f == NULL) {
    log_error("Failed to open PID file for writing: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }
  if (fprintf(f, "%d\n", getpid()) < 0) {
    log_error("Failed to write PID to file: %s", strerror(errno));
    fclose(f);
    exit(EXIT_FAILURE);
  }
  if (fclose(f) != 0) {
    log_error("Failed to close PID file: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }
}

void remove_pid_file() {
  if (unlink(PID_FILE) != 0) {
    if (errno != ENOENT) {
      log_error("Failed to remove PID file: %s", strerror(errno));
    }
  }
}

int read_pid_file() {
  FILE *f = fopen(PID_FILE, "r");
  if (f == NULL) {
    return -1;
  }
  int pid;
  int result = fscanf(f, "%d", &pid);
  if (fclose(f) != 0) {
    log_error("Failed to close PID file: %s", strerror(errno));
  }

  if (result != 1) {
    log_error("Error reading PID from file");
    return -1;
  }

  // Validate PID value
  if (pid <= 0) {
    log_error("Invalid PID in file: %d", pid);
    return -1;
  }

  return pid;
}

void daemonize() {
  pid_t pid = fork();
  if (pid < 0) {
    log_error("Failed to fork: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }
  if (pid > 0) {
    exit(EXIT_SUCCESS);
  }
  if (setsid() < 0) {
    log_error("Failed to create new session: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }

  // Setup signal actions for daemon initialization
  struct sigaction sa_ign;
  sa_ign.sa_handler = SIG_IGN;
  sigemptyset(&sa_ign.sa_mask);
  sa_ign.sa_flags = 0;

  sigaction(SIGCHLD, &sa_ign, NULL);
  sigaction(SIGHUP, &sa_ign, NULL);

  pid = fork();
  if (pid < 0) {
    log_error("Failed to fork second time: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }
  if (pid > 0) {
    exit(EXIT_SUCCESS);
  }

  umask(0);
  // chdir("/");

  // Close all open file descriptors
  long max_fd = sysconf(_SC_OPEN_MAX);
  if (max_fd == -1) {
    max_fd = 1024; // Default value
  }

  int x;
  for (x = max_fd - 1; x >= 0; x--) {
    close(x);
  }

  // Redirect standard input/output/error to /dev/null
  int fd0 = open("/dev/null", O_RDONLY);
  int fd1 = open("/dev/null", O_WRONLY);
  int fd2 = open("/dev/null", O_WRONLY);

  if (fd0 != STDIN_FILENO || fd1 != STDOUT_FILENO || fd2 != STDERR_FILENO) {
    log_fatal("Error redirecting standard file descriptors");
    exit(EXIT_FAILURE);
  }
}

void signal_handler(int signum) {
  // Use signal-safe functions
  const char msg[] = "Signal received, initiating shutdown...\n";
  write(STDERR_FILENO, msg, sizeof(msg) - 1);

  daemon_should_exit = 1;

  // For SIGTERM, perform cleanup and exit
  if (signum == SIGTERM || signum == SIGINT) {
    // Call comprehensive cleanup function
    daemon_cleanup();
    remove_pid_file();
    exit(EXIT_SUCCESS);
  }
}

void start() {
  int pid = read_pid_file();
  if (pid != -1) {
    if (kill(pid, 0) == 0) {
      log_info("Daemon already running with PID %d", pid);
      exit(EXIT_FAILURE);
    } else {
      log_info("Stale PID file found. Removing and starting new daemon");
      remove_pid_file();
    }
  }

  daemonize();
  write_pid_file();

  // Setup signal handler using sigaction for better reliability
  struct sigaction sa;
  sa.sa_handler = signal_handler;
  sigemptyset(&sa.sa_mask);
  sa.sa_flags = 0;

  // Register handlers for important signals
  if (sigaction(SIGINT, &sa, NULL) == -1) {
    log_error("Failed to set SIGINT handler: %s", strerror(errno));
  }
  if (sigaction(SIGTERM, &sa, NULL) == -1) {
    log_error("Failed to set SIGTERM handler: %s", strerror(errno));
  }
  if (sigaction(SIGQUIT, &sa, NULL) == -1) {
    log_error("Failed to set SIGQUIT handler: %s", strerror(errno));
  }
  if (sigaction(SIGHUP, &sa, NULL) == -1) {
    log_error("Failed to set SIGHUP handler: %s", strerror(errno));
  }

  // Keep some important signal handlers for debugging/monitoring
  if (sigaction(SIGUSR1, &sa, NULL) == -1) {
    log_error("Failed to set SIGUSR1 handler: %s", strerror(errno));
  }
  if (sigaction(SIGUSR2, &sa, NULL) == -1) {
    log_error("Failed to set SIGUSR2 handler: %s", strerror(errno));
  }

  daemon_start();
}

void stop() {
  int pid = read_pid_file();
  if (pid == -1) {
    log_info("No daemon running");
    exit(EXIT_FAILURE);
  }

  // First try graceful shutdown
  if (kill(pid, SIGTERM) != 0) {
    log_error("Failed to send SIGTERM to daemon: %s", strerror(errno));
    exit(EXIT_FAILURE);
  }

  // Wait for process to terminate
  int attempts = 0;
  const int max_attempts = 10;
  while (attempts < max_attempts) {
    if (kill(pid, 0) != 0) {
      // Process has terminated
      break;
    }
    sleep(1);
    attempts++;
  }

  // If process is still running, force termination
  if (attempts >= max_attempts) {
    log_warn("Process did not terminate gracefully, forcing termination");
    if (kill(pid, SIGKILL) != 0) {
      log_error("Failed to send SIGKILL to daemon: %s", strerror(errno));
    }
    sleep(1);
  }

  remove_pid_file();
  log_info("Daemon stopped");
}

void restart() {
  int pid = read_pid_file();
  if (pid != -1) {
    log_info("Stopping daemon");
    stop();
    sleep(1); // Brief wait to ensure cleanup completion
  }
  log_info("Starting daemon");
  start();
}

int daemon_main(int argc, char const *argv[]) {
  if (argc != 2) {
    log_error("Usage: %s {start|stop|restart|run}", argv[0]);
    exit(EXIT_FAILURE);
  }

  if (strcmp(argv[1], "start") == 0) {
    start();
  } else if (strcmp(argv[1], "stop") == 0) {
    stop();
  } else if (strcmp(argv[1], "restart") == 0) {
    restart();
  } else if (strcmp(argv[1], "run") == 0) {
    daemon_start();
  } else {
    log_error("Invalid argument: %s", argv[1]);
    exit(EXIT_FAILURE);
  }

  return 0;
}
