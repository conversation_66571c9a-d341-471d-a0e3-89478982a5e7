#include "background_task.h"
#include "config_loader.h"
#include "include/sitp_lib.h"
#include "ip_map.h"
#include "log.h"
#include "packet_handler.h"
#include "tcp_client.h"
#include <pthread.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>

/**
 * Initialize TCP clients for all destination IPs from the IP mapping table.
 * Uses listen_port from server configuration for all connections.
 *
 * @param config Pointer to the task configuration
 * @return 0 on success, non-zero on failure
 */
static int init_tcp_clients_from_ip_map(const task_config_t *config) {
  if (!config) {
    log_error("Invalid configuration provided");
    return -1;
  }

  const server_config_t *server_config = get_global_server_config();
  if (!server_config) {
    log_error("Failed to get server configuration");
    return -1;
  }

  char **dst_ips = NULL;
  unsigned int ip_count = 0;

  if (get_all_dst_ips(&dst_ips, &ip_count) != 0 || ip_count == 0) {
    log_error("Failed to get destination IPs from IP mapping table or no IPs found");
    log_error("No destination IPs available for TCP client initialization");
    return -1;
  }

  log_info("Initializing TCP clients for %u destination IPs...", ip_count);

  int success_count = 0;
  for (unsigned int i = 0; i < ip_count; i++) {
    if (strcmp(dst_ips[i], server_config->listen_ip) == 0) {
      log_info("Skipping TCP client initialization for IP: %s (same as listen_ip)", dst_ips[i]);
      continue;
    }

    if (init_global_tcp_client(dst_ips[i], server_config->listen_port) != 0) {
      log_error("Failed to initialize global TCP client for IP: %s", dst_ips[i]);
      // Continue with other IPs even if one fails
      continue;
    }
    log_info("Initialized TCP client for IP: %s, port: %u", dst_ips[i], server_config->listen_port);
    success_count++;
  }

  free_ip_array(dst_ips, ip_count);

  if (success_count == 0) {
    log_error("Failed to initialize any TCP clients");
    return -1;
  }

  log_info("Successfully initialized %d out of %u TCP clients", success_count, ip_count);
  return 0;
}

void *start_task(void *arg) {
  (void)arg;
  log_info("Background task starting SITP_LIB...");

  const task_config_t *config = get_global_task_config();

  if (init_tcp_clients_from_ip_map(config) != 0) {
    log_error("Failed to initialize TCP clients");
    return NULL;
  }

  log_info("Attempting to start SITP_LIB (eth_dev: %s)...", config->eth_dev);
  sitp_lib_start(config->eth_dev, config->mtu, config->protocol,
                 config->local_id, config->remote_id,
                 config->local_port, config->remote_port,
                 raw_packet_receive_callback, NULL);

  log_info("SITP_LIB has stopped");
  cleanup_global_tcp_client(NULL);
  return NULL;
}

void init_background_task(void) {
  pthread_t thread_id;
  log_info("Initializing background task module...");

  int err = pthread_create(&thread_id, NULL, &start_task, NULL);
  if (err != 0) {
    perror("Failed to create background thread");
  } else {
    log_info("Background thread created successfully");
    pthread_detach(thread_id);
  }
}
