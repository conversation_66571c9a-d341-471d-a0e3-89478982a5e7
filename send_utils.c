#define _GNU_SOURCE
#include "send_utils.h"
#include "concurrent_queue.h"
#include "config_loader.h"
#include "hex_utils.h"
#include "include/sitp_lib.h"
#include "log.h"
#include <arpa/inet.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <pthread.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static queue_t *sitp_send_queue = NULL;
static pthread_t sender_thread_id;
static volatile int keep_sender_thread_running = 1;

static void *sitp_sender_thread_func(void *arg);

static void *sitp_sender_thread_func(void *arg) {
  (void)arg; // Unused argument

  log_info("SITP sender thread started");
  while (keep_sender_thread_running) {
    uint8_t *packet_buffer = NULL;
    size_t packet_len = 0;

    // Dequeue blocks until an item is available or queue is destroyed (if
    // modified to support that)
    if (queue_dequeue(sitp_send_queue, &packet_buffer, &packet_len) == 0) {
      if (packet_buffer != NULL && packet_len > 0) {
        // Check if this is a stop signal (single byte with value 0xFF)
        if (packet_len == 1 && packet_buffer[0] == 0xFF) {
          log_info("Sender thread: Received stop signal");
          free(packet_buffer);
          break;
        }

        int send_result = sitp_lib_send(packet_buffer, packet_len);
        if (send_result != 0) {
          log_error("sitp_sender_thread_func: sitp_lib_send reported error "
                  "(code: %d) for packet of size %zu",
                  send_result, packet_len);
        } else {
          // Packet sent successfully, now apply delay if configured
          const task_config_t *config = get_global_task_config();
          if (config && config->send_delay > 0) {
            usleep(config->send_delay);
          }
        }
        free(packet_buffer); // Free the buffer as per queue_dequeue contract
      } else if (!keep_sender_thread_running) {
        // Fallback: check the running flag if we get unexpected NULL
        log_info("Sender thread: Received shutdown command");
        if (packet_buffer != NULL) {
          free(packet_buffer);
        }
        break;
      }
      // If packet_buffer is NULL but keep_sender_thread_running is true, it
      // implies an issue or a specific signal from queue_dequeue not covered
      // yet.
    } else {
      // This case implies an error with queue_dequeue itself, or it was
      // unblocked for shutdown.
      if (!keep_sender_thread_running) {
        log_info("Sender thread: Exiting due to shutdown signal (dequeue "
               "returned error or was unblocked)");
        break;
      }
      // Potentially add a small sleep here if queue_dequeue can return error
      // spuriously and not block, though typically it should block.
      log_warn("sitp_sender_thread_func: queue_dequeue failed or was "
                      "interrupted. Retrying if running");
    }
  }
  log_info("SITP sender thread stopped");
  return NULL;
}

int init_sitp_sender(size_t queue_capacity) {
  log_info("Initializing SITP sender with queue capacity %zu...",
         queue_capacity);
  if (sitp_send_queue != NULL) {
    log_error("init_sitp_sender: SITP sender already initialized");
    return -1;
  }

  sitp_send_queue = queue_create(queue_capacity);
  if (sitp_send_queue == NULL) {
    log_error("init_sitp_sender: Failed to create SITP send queue");
    return -1;
  }

  keep_sender_thread_running = 1;
  if (pthread_create(&sender_thread_id, NULL, sitp_sender_thread_func, NULL) !=
      0) {
    log_error("init_sitp_sender: Failed to create SITP sender thread");
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    return -1;
  }
  log_info("SITP sender initialized successfully");
  return 0;
}

void cleanup_sitp_sender(void) {
  log_info("Cleaning up SITP sender...");
  if (sitp_send_queue == NULL && !keep_sender_thread_running) {
    log_info("SITP sender already cleaned up or not initialized");
    return;
  }

  if (keep_sender_thread_running) {
    keep_sender_thread_running = 0;

    log_info("Signaling sender thread to stop...");

    // Send a "poison pill" (special marker) to wake up the blocked dequeue
    // operation
    if (sitp_send_queue != NULL) {
      // Enqueue a special stop signal packet (single byte with value 0xFF)
      // This will wake up the thread from pthread_cond_wait in queue_dequeue
      uint8_t stop_signal = 0xFF;
      if (queue_enqueue(sitp_send_queue, &stop_signal, 1) != 0) {
        log_error("cleanup_sitp_sender: Failed to enqueue stop signal. "
                        "Thread might be stuck");
      }
    }

    if (pthread_join(sender_thread_id, NULL) != 0) {
      log_error("cleanup_sitp_sender: Failed to join SITP sender thread. "
                      "It might be stuck");
      // Consider pthread_cancel as a last resort, but it's generally unsafe.
    } else {
      log_info("SITP sender thread joined successfully");
    }
  }

  if (sitp_send_queue != NULL) {
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    log_info("SITP send queue destroyed");
  }
  log_info("SITP sender cleanup complete");
}

int process_and_send_packet(uint8_t *buffer, size_t len) {
  log_trace("--- Queue SITP Packet Details ---");
  print_payload(buffer, len);
  log_trace("----------------------------");

  const task_config_t *config = get_global_task_config();
  if (config == NULL) {
    log_error("process_and_send_packet: Global config not loaded");
    return -1;
  }

  if (sitp_send_queue == NULL) {
    log_error("process_and_send_packet: SITP send queue is not "
                    "initialized. Dropping packet");
    return -1;
  }

  uint8_t *buffer_copy = (uint8_t *)malloc(len);
  if (buffer_copy == NULL) {
    log_error("process_and_send_packet: Failed to allocate memory for buffer "
            "copy (size: %zu)",
            len);
    return -1;
  }

  memcpy(buffer_copy, buffer, len);

  int enqueue_result = queue_enqueue(sitp_send_queue, buffer_copy, len);

  if (enqueue_result != 0) {
    log_error("process_and_send_packet: Failed to enqueue packet (size: %zu). "
            "Dropping packet",
            len);
    // The buffer_copy was not successfully enqueued, so we must free it here
    free(buffer_copy);
    return -1;
  }

  return 0;
}
